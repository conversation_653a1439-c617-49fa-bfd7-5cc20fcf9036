use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use tauri::AppHandle;

/// Represents <PERSON>'s global application settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaudiaSettings {
    /// The selected AI provider: "claude" or "gemini"
    #[serde(default = "default_provider")]
    pub provider: String,
    
    /// Provider-specific settings
    #[serde(default)]
    pub claude: ClaudeProviderSettings,
    
    #[serde(default)]
    pub gemini: GeminiProviderSettings,
    
    /// UI preferences
    #[serde(default)]
    pub ui: UiSettings,
    
    /// Other settings
    #[serde(flatten)]
    pub other: serde_json::Map<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaudeProviderSettings {
    /// Path to Claude Code binary (if custom)
    pub binary_path: Option<String>,
    
    /// Default model to use
    pub default_model: Option<String>,
    
    /// Auto-detect binary
    #[serde(default = "default_true")]
    pub auto_detect_binary: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GeminiProviderSettings {
    /// Path to Gemini CLI binary (if custom)
    pub binary_path: Option<String>,
    
    /// Default model to use
    pub default_model: Option<String>,
    
    /// Auto-detect binary
    #[serde(default = "default_true")]
    pub auto_detect_binary: bool,
    
    /// Preferred authentication method
    pub auth_method: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiSettings {
    /// Theme preference
    pub theme: Option<String>,
    
    /// Show provider indicator in UI
    #[serde(default = "default_true")]
    pub show_provider_indicator: bool,
    
    /// Auto-switch provider based on project
    #[serde(default = "default_false")]
    pub auto_switch_provider: bool,
}

impl Default for ClaudiaSettings {
    fn default() -> Self {
        Self {
            provider: default_provider(),
            claude: ClaudeProviderSettings::default(),
            gemini: GeminiProviderSettings::default(),
            ui: UiSettings::default(),
            other: serde_json::Map::new(),
        }
    }
}

impl Default for ClaudeProviderSettings {
    fn default() -> Self {
        Self {
            binary_path: None,
            default_model: None,
            auto_detect_binary: true,
        }
    }
}

impl Default for GeminiProviderSettings {
    fn default() -> Self {
        Self {
            binary_path: None,
            default_model: Some("gemini-1.5-pro".to_string()),
            auto_detect_binary: true,
            auth_method: None,
        }
    }
}

impl Default for UiSettings {
    fn default() -> Self {
        Self {
            theme: None,
            show_provider_indicator: true,
            auto_switch_provider: false,
        }
    }
}

fn default_provider() -> String {
    "claude".to_string()
}

fn default_true() -> bool {
    true
}

fn default_false() -> bool {
    false
}

/// Get the Claudia settings directory
pub fn get_claudia_settings_dir() -> Result<PathBuf, String> {
    let home_dir = dirs::home_dir()
        .ok_or_else(|| "Could not find home directory".to_string())?;
    
    let settings_dir = home_dir.join(".claudia");
    
    // Create directory if it doesn't exist
    if !settings_dir.exists() {
        fs::create_dir_all(&settings_dir)
            .map_err(|e| format!("Failed to create Claudia settings directory: {}", e))?;
    }
    
    Ok(settings_dir)
}

/// Get Claudia settings
#[tauri::command]
pub async fn get_claudia_settings() -> Result<ClaudiaSettings, String> {
    info!("Getting Claudia settings...");

    let settings_dir = get_claudia_settings_dir()?;
    let settings_file = settings_dir.join("settings.json");

    if !settings_file.exists() {
        info!("Settings file not found, returning default settings");
        return Ok(ClaudiaSettings::default());
    }

    let content = fs::read_to_string(&settings_file)
        .map_err(|e| format!("Failed to read settings.json: {}", e))?;

    let settings: ClaudiaSettings = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse settings.json: {}", e))?;

    debug!("Loaded Claudia settings: {:?}", settings);
    Ok(settings)
}

/// Save Claudia settings
#[tauri::command]
pub async fn save_claudia_settings(settings: ClaudiaSettings) -> Result<(), String> {
    info!("Saving Claudia settings...");

    let settings_dir = get_claudia_settings_dir()?;
    let settings_file = settings_dir.join("settings.json");

    let json_content = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;

    fs::write(&settings_file, json_content)
        .map_err(|e| format!("Failed to write settings file: {}", e))?;

    info!("Claudia settings saved successfully");
    Ok(())
}

/// Get the current provider from settings
#[tauri::command]
pub async fn get_current_provider() -> Result<String, String> {
    let settings = get_claudia_settings().await?;
    Ok(settings.provider)
}

/// Set the current provider in settings
#[tauri::command]
pub async fn set_current_provider(provider: String) -> Result<(), String> {
    info!("Setting current provider to: {}", provider);

    // Validate provider
    if provider != "claude" && provider != "gemini" {
        return Err(format!("Invalid provider: {}. Must be 'claude' or 'gemini'", provider));
    }

    let mut settings = get_claudia_settings().await?;
    settings.provider = provider;
    save_claudia_settings(settings).await?;

    Ok(())
}

/// Get provider-specific settings
#[tauri::command]
pub async fn get_provider_settings(provider: String) -> Result<serde_json::Value, String> {
    let settings = get_claudia_settings().await?;
    
    match provider.as_str() {
        "claude" => Ok(serde_json::to_value(settings.claude)
            .map_err(|e| format!("Failed to serialize Claude settings: {}", e))?),
        "gemini" => Ok(serde_json::to_value(settings.gemini)
            .map_err(|e| format!("Failed to serialize Gemini settings: {}", e))?),
        _ => Err(format!("Invalid provider: {}", provider)),
    }
}

/// Update provider-specific settings
#[tauri::command]
pub async fn update_provider_settings(provider: String, provider_settings: serde_json::Value) -> Result<(), String> {
    info!("Updating {} provider settings", provider);

    let mut settings = get_claudia_settings().await?;
    
    match provider.as_str() {
        "claude" => {
            settings.claude = serde_json::from_value(provider_settings)
                .map_err(|e| format!("Failed to deserialize Claude settings: {}", e))?;
        }
        "gemini" => {
            settings.gemini = serde_json::from_value(provider_settings)
                .map_err(|e| format!("Failed to deserialize Gemini settings: {}", e))?;
        }
        _ => return Err(format!("Invalid provider: {}", provider)),
    }
    
    save_claudia_settings(settings).await?;
    Ok(())
}
