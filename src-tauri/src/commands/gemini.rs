use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Stdio;
use std::time::SystemTime;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager};
use tokio::process::Command;
use uuid::Uuid;

/// Represents a Gemini CLI project discovered from ~/.gemini/tmp/
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeminiProject {
    /// Unique identifier (directory hash)
    pub id: String,
    /// Display name (derived from project path)
    pub name: String,
    /// Full path to the project directory
    pub path: String,
    /// Creation timestamp
    pub created_at: u64,
    /// Last modified timestamp
    pub last_modified: u64,
    /// Number of sessions/conversations
    pub session_count: u32,
    /// Whether the project has a GEMINI.md file
    pub has_context_file: bool,
}

/// Represents a Gemini CLI session from logs.json
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GeminiSession {
    /// Session ID from the logs
    pub session_id: String,
    /// Message ID
    pub message_id: String,
    /// Message type (usually "user")
    pub message_type: String,
    /// The actual message content
    pub message: String,
    /// Timestamp of the message
    pub timestamp: u64,
}

/// Represents Gemini CLI settings from ~/.gemini/settings.json
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeminiSettings {
    pub theme: Option<String>,
    #[serde(rename = "selectedAuthType")]
    pub selected_auth_type: Option<String>,
    #[serde(rename = "mcpServers")]
    pub mcp_servers: Option<HashMap<String, McpServerConfig>>,
    #[serde(rename = "preferredEditor")]
    pub preferred_editor: Option<String>,
    #[serde(flatten)]
    pub other: HashMap<String, serde_json::Value>,
}

/// MCP Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpServerConfig {
    pub command: String,
    pub args: Option<Vec<String>>,
    pub env: Option<HashMap<String, String>>,
    pub cwd: Option<String>,
    pub timeout: Option<u32>,
    pub trust: Option<bool>,
}

/// State for managing Gemini CLI processes
#[derive(Default)]
pub struct GeminiProcessState {
    pub current_process: tokio::sync::Mutex<Option<tokio::process::Child>>,
}

/// Get the Gemini CLI configuration directory path
fn get_gemini_config_dir() -> Result<PathBuf, String> {
    let home_dir = dirs::home_dir().ok_or("Could not determine home directory")?;
    Ok(home_dir.join(".gemini"))
}

/// Find Gemini CLI binary using the detection module
fn find_gemini_binary(app_handle: &AppHandle) -> Result<String, String> {
    crate::gemini_binary::find_gemini_binary(app_handle)
}

/// Helper function to create a tokio Command with proper environment variables
fn create_command_with_env(gemini_path: &str) -> Command {
    let mut cmd = if gemini_path.starts_with("npx") {
        let mut c = Command::new("npx");
        c.args(&["@google/gemini-cli"]);
        c
    } else {
        Command::new(gemini_path)
    };

    // Set up environment variables for Node.js and npm
    if let Ok(path) = std::env::var("PATH") {
        cmd.env("PATH", path);
    }
    
    // Ensure Node.js can find modules
    if let Ok(node_path) = std::env::var("NODE_PATH") {
        cmd.env("NODE_PATH", node_path);
    }

    cmd
}

/// List all Gemini CLI projects from ~/.gemini/tmp/
#[tauri::command]
pub async fn list_gemini_projects() -> Result<Vec<GeminiProject>, String> {
    info!("Listing Gemini CLI projects...");

    let config_dir = get_gemini_config_dir()?;
    let tmp_dir = config_dir.join("tmp");

    if !tmp_dir.exists() {
        info!("Gemini tmp directory does not exist: {:?}", tmp_dir);
        return Ok(vec![]);
    }

    let mut projects = Vec::new();

    let entries = fs::read_dir(&tmp_dir)
        .map_err(|e| format!("Failed to read Gemini tmp directory: {}", e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();

        if path.is_dir() {
            let dir_name = path
                .file_name()
                .and_then(|n| n.to_str())
                .ok_or_else(|| "Invalid directory name".to_string())?;

            // Get directory metadata
            let metadata = fs::metadata(&path)
                .map_err(|e| format!("Failed to read directory metadata: {}", e))?;

            let created_at = metadata
                .created()
                .or_else(|_| metadata.modified())
                .unwrap_or(SystemTime::UNIX_EPOCH)
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            let last_modified = metadata
                .modified()
                .unwrap_or(SystemTime::UNIX_EPOCH)
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            // Try to get the actual project path by reverse-engineering the hash
            let project_path = get_project_path_from_hash(dir_name).unwrap_or_else(|| {
                format!("Unknown project ({})", &dir_name[..8])
            });

            // Count sessions by checking logs.json
            let logs_file = path.join("logs.json");
            let session_count = if logs_file.exists() {
                match fs::read_to_string(&logs_file) {
                    Ok(content) => {
                        match serde_json::from_str::<Vec<GeminiSession>>(&content) {
                            Ok(sessions) => sessions.len() as u32,
                            Err(_) => 0,
                        }
                    }
                    Err(_) => 0,
                }
            } else {
                0
            };

            // Check for GEMINI.md file in the project directory
            let has_context_file = if let Ok(actual_path) = std::fs::canonicalize(&project_path) {
                actual_path.join("GEMINI.md").exists()
            } else {
                false
            };

            let project = GeminiProject {
                id: dir_name.to_string(),
                name: Path::new(&project_path)
                    .file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("Unknown")
                    .to_string(),
                path: project_path,
                created_at,
                last_modified,
                session_count,
                has_context_file,
            };

            projects.push(project);
        }
    }

    // Sort by last modified (most recent first)
    projects.sort_by(|a, b| b.last_modified.cmp(&a.last_modified));

    info!("Found {} Gemini projects", projects.len());
    Ok(projects)
}

/// Get sessions for a specific Gemini project
#[tauri::command]
pub async fn get_gemini_project_sessions(project_id: String) -> Result<Vec<GeminiSession>, String> {
    info!("Getting sessions for Gemini project: {}", project_id);

    let config_dir = get_gemini_config_dir()?;
    let project_dir = config_dir.join("tmp").join(&project_id);
    let logs_file = project_dir.join("logs.json");

    if !logs_file.exists() {
        return Ok(vec![]);
    }

    let content = fs::read_to_string(&logs_file)
        .map_err(|e| format!("Failed to read logs.json: {}", e))?;

    let sessions: Vec<GeminiSession> = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse logs.json: {}", e))?;

    Ok(sessions)
}

/// Get Gemini CLI settings
#[tauri::command]
pub async fn get_gemini_settings() -> Result<GeminiSettings, String> {
    info!("Getting Gemini CLI settings...");

    let config_dir = get_gemini_config_dir()?;
    let settings_file = config_dir.join("settings.json");

    if !settings_file.exists() {
        // Return default settings if file doesn't exist
        return Ok(GeminiSettings {
            theme: None,
            selected_auth_type: None,
            mcp_servers: None,
            preferred_editor: None,
            other: HashMap::new(),
        });
    }

    let content = fs::read_to_string(&settings_file)
        .map_err(|e| format!("Failed to read settings.json: {}", e))?;

    let settings: GeminiSettings = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse settings.json: {}", e))?;

    Ok(settings)
}

/// Execute a new Gemini CLI session with streaming output
#[tauri::command]
pub async fn execute_gemini_cli(
    app: AppHandle,
    project_path: String,
    prompt: String,
    model: Option<String>,
) -> Result<(), String> {
    info!(
        "Starting new Gemini CLI session in: {} with model: {:?}",
        project_path, model
    );

    let gemini_path = find_gemini_binary(&app)?;
    let mut cmd = create_command_with_env(&gemini_path);

    // Add prompt
    cmd.arg("--prompt").arg(&prompt);

    // Add model if specified
    if let Some(m) = model {
        cmd.arg("--model").arg(&m);
    }

    // Set working directory
    cmd.current_dir(&project_path)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped());

    spawn_gemini_process(app, cmd).await
}

/// Helper function to spawn Gemini process and handle streaming
async fn spawn_gemini_process(app: AppHandle, mut cmd: Command) -> Result<(), String> {
    use tokio::io::{AsyncBufReadExt, BufReader};

    // Generate a unique session ID for this Gemini CLI session
    let session_id = format!(
        "gemini-{}-{}",
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis(),
        Uuid::new_v4().to_string()
    );

    // Spawn the process
    let mut child = cmd
        .spawn()
        .map_err(|e| format!("Failed to spawn Gemini CLI: {}", e))?;

    // Get stdout and stderr
    let stdout = child.stdout.take().ok_or("Failed to get stdout")?;
    let stderr = child.stderr.take().ok_or("Failed to get stderr")?;

    // Get the child PID for logging
    let pid = child.id();
    info!(
        "Spawned Gemini CLI process with PID: {:?} and session ID: {}",
        pid, session_id
    );

    // Create readers
    let stdout_reader = BufReader::new(stdout);
    let stderr_reader = BufReader::new(stderr);

    // Store the child process in the global state
    let gemini_state = app.state::<GeminiProcessState>();
    {
        let mut current_process = gemini_state.current_process.lock().await;
        // If there's already a process running, kill it first
        if let Some(mut existing_child) = current_process.take() {
            warn!("Killing existing Gemini process before starting new one");
            let _ = existing_child.kill().await;
        }
        *current_process = Some(child);
    }

    // Handle stdout in a separate task
    let app_handle_stdout = app.clone();
    let session_id_clone1 = session_id.clone();
    tokio::spawn(async move {
        let mut lines = stdout_reader.lines();
        while let Ok(Some(line)) = lines.next_line().await {
            debug!("Gemini stdout: {}", line);
            let _ = app_handle_stdout.emit(&format!("gemini-output:{}", session_id_clone1), &line);
            // Also emit to generic event for backward compatibility
            let _ = app_handle_stdout.emit("gemini-output", &line);
        }
    });

    // Handle stderr in a separate task
    let app_handle_stderr = app.clone();
    let session_id_clone2 = session_id.clone();
    tokio::spawn(async move {
        let mut lines = stderr_reader.lines();
        while let Ok(Some(line)) = lines.next_line().await {
            debug!("Gemini stderr: {}", line);
            let _ = app_handle_stderr.emit(&format!("gemini-error:{}", session_id_clone2), &line);
            // Also emit to generic event for backward compatibility
            let _ = app_handle_stderr.emit("gemini-error", &line);
        }
    });

    // Wait for the process to complete in a separate task
    let app_handle_wait = app.clone();
    let session_id_clone3 = session_id.clone();
    tokio::spawn(async move {
        let gemini_state_wait = app_handle_wait.state::<GeminiProcessState>();
        let mut current_process = gemini_state_wait.current_process.lock().await;
        if let Some(ref mut child) = *current_process {
            match child.wait().await {
                Ok(status) => {
                    info!("Gemini process completed with status: {:?}", status);
                    let success = status.success();
                    // Add a small delay to ensure all messages are processed
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    let _ = app_handle_wait
                        .emit(&format!("gemini-complete:{}", session_id_clone3), success);
                    // Also emit to generic event for backward compatibility
                    let _ = app_handle_wait.emit("gemini-complete", success);
                }
                Err(e) => {
                    error!("Failed to wait for Gemini process: {}", e);
                    // Add a small delay to ensure all messages are processed
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    let _ = app_handle_wait
                        .emit(&format!("gemini-complete:{}", session_id_clone3), false);
                    // Also emit to generic event for backward compatibility
                    let _ = app_handle_wait.emit("gemini-complete", false);
                }
            }
        }

        // Clear the process from state
        *current_process = None;
    });

    // Return the session ID to the frontend
    let _ = app.emit(
        &format!("gemini-session-started:{}", session_id),
        session_id.clone(),
    );

    Ok(())
}

/// Cancel the currently running Gemini CLI execution
#[tauri::command]
pub async fn cancel_gemini_execution(
    app: AppHandle,
    session_id: Option<String>,
) -> Result<(), String> {
    info!(
        "Cancelling Gemini CLI execution for session: {:?}",
        session_id
    );

    let gemini_state = app.state::<GeminiProcessState>();
    let mut current_process = gemini_state.current_process.lock().await;

    if let Some(mut child) = current_process.take() {
        // Kill the process
        match child.kill().await {
            Ok(_) => {
                info!("Successfully killed Gemini process");

                // If we have a session ID, emit session-specific events
                if let Some(sid) = session_id {
                    let _ = app.emit(&format!("gemini-cancelled:{}", sid), true);
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    let _ = app.emit(&format!("gemini-complete:{}", sid), false);
                }

                // Also emit generic events for backward compatibility
                let _ = app.emit("gemini-cancelled", true);
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                let _ = app.emit("gemini-complete", false);
                Ok(())
            }
            Err(e) => {
                error!("Failed to kill Gemini process: {}", e);
                Err(format!("Failed to kill Gemini process: {}", e))
            }
        }
    } else {
        warn!("No Gemini process is currently running");
        Ok(())
    }
}

/// OAuth credentials structure for Gemini CLI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeminiOAuthCreds {
    pub access_token: String,
    pub expiry_date: u64,
    pub id_token: Option<String>,
    pub refresh_token: String,
    pub scope: String,
    pub token_type: String,
}

/// Authentication status for Gemini CLI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeminiAuthStatus {
    pub is_authenticated: bool,
    pub auth_type: String, // "oauth-personal", "oauth-enterprise", "api-key", "vertex-ai"
    pub user_id: Option<String>,
    pub expires_at: Option<u64>,
    pub needs_refresh: bool,
}

/// Check Gemini CLI authentication status
#[tauri::command]
pub async fn get_gemini_auth_status() -> Result<GeminiAuthStatus, String> {
    info!("Checking Gemini CLI authentication status...");

    let config_dir = get_gemini_config_dir()?;

    // Check for OAuth credentials
    let oauth_file = config_dir.join("oauth_creds.json");
    if oauth_file.exists() {
        match fs::read_to_string(&oauth_file) {
            Ok(content) => {
                match serde_json::from_str::<GeminiOAuthCreds>(&content) {
                    Ok(creds) => {
                        let now = std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap_or_default()
                            .as_secs();

                        let needs_refresh = creds.expiry_date <= now;

                        // Get user ID if available
                        let user_id = if let Ok(user_id_content) = fs::read_to_string(config_dir.join("user_id")) {
                            Some(user_id_content.trim().to_string())
                        } else {
                            None
                        };

                        // Get auth type from settings
                        let auth_type = match get_gemini_settings().await {
                            Ok(settings) => settings.selected_auth_type.unwrap_or_else(|| "oauth-personal".to_string()),
                            Err(_) => "oauth-personal".to_string(),
                        };

                        return Ok(GeminiAuthStatus {
                            is_authenticated: !needs_refresh,
                            auth_type,
                            user_id,
                            expires_at: Some(creds.expiry_date),
                            needs_refresh,
                        });
                    }
                    Err(e) => {
                        warn!("Failed to parse OAuth credentials: {}", e);
                    }
                }
            }
            Err(e) => {
                warn!("Failed to read OAuth credentials: {}", e);
            }
        }
    }

    // Check for API key in environment
    if std::env::var("GEMINI_API_KEY").is_ok() {
        return Ok(GeminiAuthStatus {
            is_authenticated: true,
            auth_type: "api-key".to_string(),
            user_id: None,
            expires_at: None,
            needs_refresh: false,
        });
    }

    // Check for Vertex AI authentication
    if std::env::var("GOOGLE_CLOUD_PROJECT").is_ok() {
        return Ok(GeminiAuthStatus {
            is_authenticated: true,
            auth_type: "vertex-ai".to_string(),
            user_id: None,
            expires_at: None,
            needs_refresh: false,
        });
    }

    // No authentication found
    Ok(GeminiAuthStatus {
        is_authenticated: false,
        auth_type: "none".to_string(),
        user_id: None,
        expires_at: None,
        needs_refresh: false,
    })
}

/// Trigger Gemini CLI authentication flow
#[tauri::command]
pub async fn authenticate_gemini_cli(
    app: AppHandle,
    auth_type: String, // "oauth-personal", "oauth-enterprise", "api-key", "vertex-ai"
    api_key: Option<String>,
) -> Result<GeminiAuthStatus, String> {
    info!("Starting Gemini CLI authentication flow: {}", auth_type);

    match auth_type.as_str() {
        "api-key" => {
            if let Some(key) = api_key {
                // Set the API key environment variable
                std::env::set_var("GEMINI_API_KEY", &key);

                // Test the API key by running a simple command
                let gemini_path = find_gemini_binary(&app)?;
                let mut cmd = create_command_with_env(&gemini_path);
                cmd.arg("--version")
                    .env("GEMINI_API_KEY", &key);

                match cmd.output().await {
                    Ok(output) => {
                        if output.status.success() {
                            info!("API key authentication successful");
                            return Ok(GeminiAuthStatus {
                                is_authenticated: true,
                                auth_type: "api-key".to_string(),
                                user_id: None,
                                expires_at: None,
                                needs_refresh: false,
                            });
                        } else {
                            let error = String::from_utf8_lossy(&output.stderr);
                            return Err(format!("API key validation failed: {}", error));
                        }
                    }
                    Err(e) => {
                        return Err(format!("Failed to test API key: {}", e));
                    }
                }
            } else {
                return Err("API key is required for API key authentication".to_string());
            }
        }
        "oauth-personal" | "oauth-enterprise" => {
            // For OAuth, we need to launch the Gemini CLI auth flow
            let _gemini_path = find_gemini_binary(&app)?;

            // This would typically launch an interactive auth flow
            // For now, we'll return an error suggesting manual authentication
            return Err("OAuth authentication requires manual setup. Please run 'gemini auth' in your terminal to authenticate.".to_string());
        }
        "vertex-ai" => {
            // Check if Google Cloud SDK is configured
            if std::env::var("GOOGLE_CLOUD_PROJECT").is_err() {
                return Err("Vertex AI authentication requires GOOGLE_CLOUD_PROJECT environment variable to be set".to_string());
            }

            // Test Vertex AI authentication
            let gemini_path = find_gemini_binary(&app)?;
            let mut cmd = create_command_with_env(&gemini_path);
            cmd.arg("--version");

            match cmd.output().await {
                Ok(output) => {
                    if output.status.success() {
                        info!("Vertex AI authentication successful");
                        return Ok(GeminiAuthStatus {
                            is_authenticated: true,
                            auth_type: "vertex-ai".to_string(),
                            user_id: None,
                            expires_at: None,
                            needs_refresh: false,
                        });
                    } else {
                        let error = String::from_utf8_lossy(&output.stderr);
                        return Err(format!("Vertex AI authentication failed: {}", error));
                    }
                }
                Err(e) => {
                    return Err(format!("Failed to test Vertex AI authentication: {}", e));
                }
            }
        }
        _ => {
            return Err(format!("Unsupported authentication type: {}", auth_type));
        }
    }
}

/// Clear Gemini CLI authentication
#[tauri::command]
pub async fn clear_gemini_auth() -> Result<(), String> {
    info!("Clearing Gemini CLI authentication...");

    let config_dir = get_gemini_config_dir()?;

    // Remove OAuth credentials
    let oauth_file = config_dir.join("oauth_creds.json");
    if oauth_file.exists() {
        fs::remove_file(&oauth_file)
            .map_err(|e| format!("Failed to remove OAuth credentials: {}", e))?;
    }

    // Remove user ID
    let user_id_file = config_dir.join("user_id");
    if user_id_file.exists() {
        fs::remove_file(&user_id_file)
            .map_err(|e| format!("Failed to remove user ID: {}", e))?;
    }

    // Clear environment variables
    std::env::remove_var("GEMINI_API_KEY");

    info!("Gemini CLI authentication cleared");
    Ok(())
}

/// Save Gemini CLI settings to ~/.gemini/settings.json
#[tauri::command]
pub async fn save_gemini_settings(settings: GeminiSettings) -> Result<(), String> {
    info!("Saving Gemini CLI settings...");

    let config_dir = get_gemini_config_dir()?;
    let settings_file = config_dir.join("settings.json");

    // Ensure the config directory exists
    if !config_dir.exists() {
        fs::create_dir_all(&config_dir)
            .map_err(|e| format!("Failed to create Gemini config directory: {}", e))?;
    }

    let json_content = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;

    fs::write(&settings_file, json_content)
        .map_err(|e| format!("Failed to write settings file: {}", e))?;

    info!("Gemini CLI settings saved successfully");
    Ok(())
}

/// Read GEMINI.md file from a project directory
#[tauri::command]
pub async fn read_gemini_md_file(project_path: String) -> Result<String, String> {
    info!("Reading GEMINI.md file from: {}", project_path);

    let gemini_md_path = Path::new(&project_path).join("GEMINI.md");

    if !gemini_md_path.exists() {
        return Err("GEMINI.md file not found in project directory".to_string());
    }

    fs::read_to_string(&gemini_md_path)
        .map_err(|e| format!("Failed to read GEMINI.md file: {}", e))
}

/// Save GEMINI.md file to a project directory
#[tauri::command]
pub async fn save_gemini_md_file(project_path: String, content: String) -> Result<(), String> {
    info!("Saving GEMINI.md file to: {}", project_path);

    let gemini_md_path = Path::new(&project_path).join("GEMINI.md");

    fs::write(&gemini_md_path, content)
        .map_err(|e| format!("Failed to write GEMINI.md file: {}", e))?;

    info!("GEMINI.md file saved successfully");
    Ok(())
}

/// Find all GEMINI.md files in a project directory and subdirectories
#[tauri::command]
pub async fn find_gemini_md_files(project_path: String) -> Result<Vec<String>, String> {
    info!("Finding GEMINI.md files in: {}", project_path);

    let mut gemini_files = Vec::new();
    let project_dir = Path::new(&project_path);

    if !project_dir.exists() {
        return Err("Project directory does not exist".to_string());
    }

    fn find_files_recursive(dir: &Path, files: &mut Vec<String>) -> Result<(), std::io::Error> {
        let entries = fs::read_dir(dir)?;

        for entry in entries {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() && path.file_name() == Some(std::ffi::OsStr::new("GEMINI.md")) {
                if let Some(path_str) = path.to_str() {
                    files.push(path_str.to_string());
                }
            } else if path.is_dir() {
                // Skip hidden directories and common ignore patterns
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if !dir_name.starts_with('.')
                        && dir_name != "node_modules"
                        && dir_name != "target"
                        && dir_name != "dist"
                        && dir_name != "build" {
                        find_files_recursive(&path, files)?;
                    }
                }
            }
        }

        Ok(())
    }

    find_files_recursive(project_dir, &mut gemini_files)
        .map_err(|e| format!("Failed to search for GEMINI.md files: {}", e))?;

    info!("Found {} GEMINI.md files", gemini_files.len());
    Ok(gemini_files)
}

/// Get environment variables relevant to Gemini CLI
#[tauri::command]
pub async fn get_gemini_environment() -> Result<HashMap<String, String>, String> {
    info!("Getting Gemini CLI environment variables...");

    let mut env_vars = HashMap::new();

    // Check for Gemini API key
    if let Ok(api_key) = std::env::var("GEMINI_API_KEY") {
        env_vars.insert("GEMINI_API_KEY".to_string(), api_key);
    }

    // Check for Google Cloud project
    if let Ok(project) = std::env::var("GOOGLE_CLOUD_PROJECT") {
        env_vars.insert("GOOGLE_CLOUD_PROJECT".to_string(), project);
    }

    // Check for Google Application Credentials
    if let Ok(creds) = std::env::var("GOOGLE_APPLICATION_CREDENTIALS") {
        env_vars.insert("GOOGLE_APPLICATION_CREDENTIALS".to_string(), creds);
    }

    // Check for Node.js related variables
    if let Ok(node_path) = std::env::var("NODE_PATH") {
        env_vars.insert("NODE_PATH".to_string(), node_path);
    }

    if let Ok(npm_config_prefix) = std::env::var("NPM_CONFIG_PREFIX") {
        env_vars.insert("NPM_CONFIG_PREFIX".to_string(), npm_config_prefix);
    }

    Ok(env_vars)
}

/// Set environment variable for Gemini CLI
#[tauri::command]
pub async fn set_gemini_environment_variable(key: String, value: String) -> Result<(), String> {
    info!("Setting Gemini environment variable: {}", key);

    // Validate that this is a safe environment variable to set
    match key.as_str() {
        "GEMINI_API_KEY" | "GOOGLE_CLOUD_PROJECT" | "GOOGLE_APPLICATION_CREDENTIALS" => {
            std::env::set_var(&key, &value);
            info!("Environment variable {} set successfully", key);
            Ok(())
        }
        _ => {
            Err(format!("Setting environment variable '{}' is not allowed for security reasons", key))
        }
    }
}

/// Helper function to attempt to reverse-engineer project path from hash
/// This is a best-effort approach since we don't have the original mapping
fn get_project_path_from_hash(_hash: &str) -> Option<String> {
    // This would require maintaining a mapping or trying common project paths
    // For now, we'll return None and let the caller handle it
    // In a real implementation, you might want to:
    // 1. Check common project directories
    // 2. Maintain a mapping file
    // 3. Use the logs.json content to infer the path
    None
}
