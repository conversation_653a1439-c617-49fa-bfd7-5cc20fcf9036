use anyhow::Result;
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use std::cmp::Ordering;
use std::path::PathBuf;
use std::process::Command;
use tauri::Manager;

/// Represents a Gemini CLI installation with metadata
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GeminiInstallation {
    /// Full path to the Gemini binary or command
    pub path: String,
    /// Version string if available
    pub version: Option<String>,
    /// Source of discovery (e.g., "npm-global", "npx", "local", "which")
    pub source: String,
    /// Whether this requires Node.js runtime
    pub requires_node: bool,
    /// Command type: "binary" or "npx"
    pub command_type: String,
}

/// Main function to find the Gemini CLI
/// Checks database first, then discovers all installations and selects the best one
pub fn find_gemini_binary(app_handle: &tauri::AppHandle) -> Result<String, String> {
    info!("Searching for gemini CLI...");

    // First check if we have a stored path in the database
    if let Ok(app_data_dir) = app_handle.path().app_data_dir() {
        let db_path = app_data_dir.join("agents.db");
        if db_path.exists() {
            if let Ok(conn) = rusqlite::Connection::open(&db_path) {
                if let Ok(stored_path) = conn.query_row(
                    "SELECT value FROM app_settings WHERE key = 'gemini_binary_path'",
                    [],
                    |row| row.get::<_, String>(0),
                ) {
                    info!("Found stored gemini path in database: {}", stored_path);
                    if validate_gemini_path(&stored_path) {
                        return Ok(stored_path);
                    } else {
                        warn!("Stored gemini path no longer valid: {}", stored_path);
                    }
                }
            }
        }
    }

    // Discover all available installations
    let installations = discover_all_installations();

    if installations.is_empty() {
        error!("Could not find gemini CLI in any location");
        return Err("Gemini CLI not found. Please install it using: npm install -g @google/gemini-cli".to_string());
    }

    // Log all found installations
    for installation in &installations {
        info!("Found Gemini installation: {:?}", installation);
    }

    // Select the best installation
    if let Some(best) = select_best_installation(installations) {
        info!(
            "Selected Gemini installation: path={}, version={:?}, source={}",
            best.path, best.version, best.source
        );
        Ok(best.path)
    } else {
        Err("No valid Gemini installation found".to_string())
    }
}

/// Discovers all available Gemini CLI installations and returns them for selection
pub fn discover_gemini_installations() -> Vec<GeminiInstallation> {
    info!("Discovering all Gemini CLI installations...");

    let installations = discover_all_installations();

    // Sort by preference: direct binary > npm global > npx
    let mut sorted = installations;
    sorted.sort_by(|a, b| {
        match (&a.version, &b.version) {
            (Some(v1), Some(v2)) => {
                match compare_versions(v2, v1) {
                    Ordering::Equal => source_preference(a).cmp(&source_preference(b)),
                    other => other,
                }
            }
            (Some(_), None) => Ordering::Less,
            (None, Some(_)) => Ordering::Greater,
            (None, None) => source_preference(a).cmp(&source_preference(b)),
        }
    });

    sorted
}

/// Returns a preference score for installation sources (lower is better)
fn source_preference(installation: &GeminiInstallation) -> u8 {
    match installation.source.as_str() {
        "which" => 1,
        "npm-global" => 2,
        "homebrew" => 3,
        "local-npm" => 4,
        "npx" => 5,
        "yarn-global" => 6,
        "pnpm-global" => 7,
        "bun-global" => 8,
        _ => 9,
    }
}

/// Discovers all Gemini CLI installations on the system
fn discover_all_installations() -> Vec<GeminiInstallation> {
    let mut installations = Vec::new();

    // 1. Try 'which' command first
    if let Some(installation) = try_which_command() {
        installations.push(installation);
    }

    // 2. Check npm global installations
    installations.extend(find_npm_installations());

    // 3. Check npx availability
    if let Some(installation) = check_npx_availability() {
        installations.push(installation);
    }

    // 4. Check other package managers
    installations.extend(find_other_package_managers());

    // Remove duplicates by path
    let mut unique_paths = std::collections::HashSet::new();
    installations.retain(|install| unique_paths.insert(install.path.clone()));

    installations
}

/// Try using the 'which' command to find Gemini CLI
fn try_which_command() -> Option<GeminiInstallation> {
    debug!("Trying 'which gemini' to find binary...");

    match Command::new("which").arg("gemini").output() {
        Ok(output) if output.status.success() => {
            let output_str = String::from_utf8_lossy(&output.stdout).trim().to_string();

            if output_str.is_empty() {
                return None;
            }

            debug!("'which' found gemini at: {}", output_str);

            // Verify the path exists
            if !PathBuf::from(&output_str).exists() {
                warn!("Path from 'which' does not exist: {}", output_str);
                return None;
            }

            // Get version
            let version = get_gemini_version(&output_str).ok().flatten();

            Some(GeminiInstallation {
                path: output_str,
                version,
                source: "which".to_string(),
                requires_node: true,
                command_type: "binary".to_string(),
            })
        }
        _ => None,
    }
}

/// Find Gemini CLI installations via npm
fn find_npm_installations() -> Vec<GeminiInstallation> {
    let mut installations = Vec::new();

    // Check npm global installation
    if let Ok(output) = Command::new("npm")
        .args(&["list", "-g", "@google/gemini-cli", "--depth=0"])
        .output()
    {
        if output.status.success() {
            let output_str = String::from_utf8_lossy(&output.stdout);
            if output_str.contains("@google/gemini-cli") {
                debug!("Found Gemini CLI via npm global");

                // Try to get the actual binary path
                if let Ok(npm_bin_output) = Command::new("npm").args(&["bin", "-g"]).output() {
                    if npm_bin_output.status.success() {
                        let bin_dir = String::from_utf8_lossy(&npm_bin_output.stdout).trim();
                        let gemini_path = format!("{}/gemini", bin_dir);

                        if PathBuf::from(&gemini_path).exists() {
                            let version = get_gemini_version(&gemini_path).ok().flatten();

                            installations.push(GeminiInstallation {
                                path: gemini_path,
                                version,
                                source: "npm-global".to_string(),
                                requires_node: true,
                                command_type: "binary".to_string(),
                            });
                        }
                    }
                }
            }
        }
    }

    installations
}

/// Check if npx can run Gemini CLI
fn check_npx_availability() -> Option<GeminiInstallation> {
    debug!("Checking npx availability for @google/gemini-cli...");

    // Check if npx is available
    if Command::new("which").arg("npx").output().is_err() {
        return None;
    }

    // Try to get version via npx (this will download if not cached)
    match Command::new("npx")
        .args(&["@google/gemini-cli", "--version"])
        .output()
    {
        Ok(output) if output.status.success() => {
            let version_str = String::from_utf8_lossy(&output.stdout).trim();
            let version = if version_str.is_empty() {
                None
            } else {
                Some(version_str.to_string())
            };

            debug!("npx can execute @google/gemini-cli, version: {:?}", version);

            Some(GeminiInstallation {
                path: "npx @google/gemini-cli".to_string(),
                version,
                source: "npx".to_string(),
                requires_node: true,
                command_type: "npx".to_string(),
            })
        }
        _ => {
            debug!("npx cannot execute @google/gemini-cli");
            None
        }
    }
}

/// Check other package managers (yarn, pnpm, bun)
fn find_other_package_managers() -> Vec<GeminiInstallation> {
    let mut installations = Vec::new();

    // Check yarn global
    if let Ok(output) = Command::new("yarn")
        .args(&["global", "list", "--pattern", "@google/gemini-cli"])
        .output()
    {
        if output.status.success() {
            let output_str = String::from_utf8_lossy(&output.stdout);
            if output_str.contains("@google/gemini-cli") {
                debug!("Found Gemini CLI via yarn global");
                // Similar logic to npm, but yarn global bin path detection is more complex
                // For now, we'll just note it's available
            }
        }
    }

    installations
}

/// Get Gemini CLI version by running --version command
fn get_gemini_version(path: &str) -> Result<Option<String>, String> {
    let mut cmd = if path.starts_with("npx") {
        let mut c = Command::new("npx");
        c.args(&["@google/gemini-cli", "--version"]);
        c
    } else {
        let mut c = Command::new(path);
        c.arg("--version");
        c
    };

    match cmd.output() {
        Ok(output) => {
            if output.status.success() {
                let version_str = String::from_utf8_lossy(&output.stdout).trim();
                if version_str.is_empty() {
                    Ok(None)
                } else {
                    Ok(Some(version_str.to_string()))
                }
            } else {
                Ok(None)
            }
        }
        Err(e) => {
            warn!("Failed to get version for {}: {}", path, e);
            Ok(None)
        }
    }
}

/// Validate that a Gemini CLI path is still functional
pub fn validate_gemini_path(path: &str) -> bool {
    if path.starts_with("npx") {
        // For npx commands, just check if npx is available
        Command::new("which").arg("npx").output().is_ok()
    } else {
        // For direct paths, check if file exists
        PathBuf::from(path).exists()
    }
}

/// Select the best installation based on version and source preference
fn select_best_installation(installations: Vec<GeminiInstallation>) -> Option<GeminiInstallation> {
    installations.into_iter().max_by(|a, b| {
        match (&a.version, &b.version) {
            (Some(v1), Some(v2)) => compare_versions(v1, v2),
            (Some(_), None) => Ordering::Greater,
            (None, Some(_)) => Ordering::Less,
            (None, None) => {
                // Prefer non-npx installations when no version info
                if a.command_type == "binary" && b.command_type == "npx" {
                    Ordering::Greater
                } else if a.command_type == "npx" && b.command_type == "binary" {
                    Ordering::Less
                } else {
                    source_preference(a).cmp(&source_preference(b)).reverse()
                }
            }
        }
    })
}

/// Compare two version strings (simple semantic version comparison)
fn compare_versions(a: &str, b: &str) -> Ordering {
    let a_parts: Vec<u32> = a
        .split('.')
        .filter_map(|s| {
            s.chars()
                .take_while(|c| c.is_numeric())
                .collect::<String>()
                .parse()
                .ok()
        })
        .collect();

    let b_parts: Vec<u32> = b
        .split('.')
        .filter_map(|s| {
            s.chars()
                .take_while(|c| c.is_numeric())
                .collect::<String>()
                .parse()
                .ok()
        })
        .collect();

    for i in 0..std::cmp::max(a_parts.len(), b_parts.len()) {
        let a_val = a_parts.get(i).unwrap_or(&0);
        let b_val = b_parts.get(i).unwrap_or(&0);
        match a_val.cmp(b_val) {
            Ordering::Equal => continue,
            other => return other,
        }
    }

    Ordering::Equal
}

/// Helper function to create a Command for Gemini CLI execution
pub fn create_gemini_command(gemini_path: &str) -> Command {
    if gemini_path.starts_with("npx") {
        let mut cmd = Command::new("npx");
        cmd.args(&["@google/gemini-cli"]);
        cmd
    } else {
        Command::new(gemini_path)
    }
}
