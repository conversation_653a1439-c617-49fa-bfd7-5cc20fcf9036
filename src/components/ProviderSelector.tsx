import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Check,
  AlertCircle,
  Loader2
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { 
  api, 
  type ClaudiaSettings,
  type ClaudeInstallation,
  type GeminiInstallation 
} from "@/lib/api";
import { cn } from "@/lib/utils";

interface ProviderSelectorProps {
  className?: string;
  onProviderChange?: (provider: string) => void;
}

/**
 * Provider selection component for switching between Claude Code and Gemini CLI
 */
export const ProviderSelector: React.FC<ProviderSelectorProps> = ({
  className,
  onProviderChange,
}) => {
  const [settings, setSettings] = useState<ClaudiaSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [claudeInstallations, setClaudeInstallations] = useState<ClaudeInstallation[]>([]);
  const [geminiInstallations, setGeminiInstallations] = useState<GeminiInstallation[]>([]);

  useEffect(() => {
    loadSettings();
    loadInstallations();
  }, []);

  const loadSettings = async () => {
    try {
      const claudiaSettings = await api.getClaudiaSettings();
      setSettings(claudiaSettings);
    } catch (err) {
      console.error("Failed to load settings:", err);
      setError("Failed to load settings");
    } finally {
      setLoading(false);
    }
  };

  const loadInstallations = async () => {
    try {
      const [claude, gemini] = await Promise.all([
        api.listClaudeInstallations().catch(() => []),
        api.listGeminiInstallations().catch(() => [])
      ]);
      setClaudeInstallations(claude);
      setGeminiInstallations(gemini);
    } catch (err) {
      console.error("Failed to load installations:", err);
    }
  };

  const handleProviderChange = async (provider: string) => {
    if (!settings) return;

    setSaving(true);
    try {
      await api.setCurrentProvider(provider);
      setSettings({ ...settings, provider });
      onProviderChange?.(provider);
    } catch (err) {
      console.error("Failed to change provider:", err);
      setError("Failed to change provider");
    } finally {
      setSaving(false);
    }
  };

  const handleSettingChange = async (key: keyof ClaudiaSettings, value: any) => {
    if (!settings) return;

    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);

    try {
      await api.saveClaudiaSettings(newSettings);
    } catch (err) {
      console.error("Failed to save settings:", err);
      setError("Failed to save settings");
    }
  };

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center py-8", className)}>
        <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className={cn("p-4", className)}>
        <div className="flex items-center gap-2 text-sm text-destructive">
          <AlertCircle className="w-4 h-4" />
          {error}
        </div>
      </Card>
    );
  }

  if (!settings) {
    return null;
  }

  const claudeAvailable = claudeInstallations.length > 0;
  const geminiAvailable = geminiInstallations.length > 0;

  return (
    <div className={cn("space-y-6", className)}>
      <Card className="p-6">
        <div className="space-y-6">
          <div>
            <h3 className="text-base font-semibold mb-2">AI Provider</h3>
            <p className="text-sm text-muted-foreground">
              Choose between Claude Code and Gemini CLI for AI assistance
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Claude Code Option */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={cn(
                "relative p-4 border-2 rounded-lg cursor-pointer transition-all",
                settings.provider === "claude"
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50",
                !claudeAvailable && "opacity-50 cursor-not-allowed"
              )}
              onClick={() => claudeAvailable && handleProviderChange("claude")}
            >
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-md bg-orange-100 dark:bg-orange-900/20">
                  <Bot className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">Claude Code</h4>
                    {settings.provider === "claude" && (
                      <Check className="w-4 h-4 text-primary" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Anthropic's official Claude Code CLI
                  </p>
                  <div className="mt-2 text-xs">
                    {claudeAvailable ? (
                      <span className="text-green-600 dark:text-green-400">
                        ✓ {claudeInstallations.length} installation{claudeInstallations.length !== 1 ? 's' : ''} found
                      </span>
                    ) : (
                      <span className="text-muted-foreground">
                        No installations found
                      </span>
                    )}
                  </div>
                </div>
              </div>
              {saving && settings.provider === "claude" && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-lg">
                  <Loader2 className="w-4 h-4 animate-spin" />
                </div>
              )}
            </motion.div>

            {/* Gemini CLI Option */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={cn(
                "relative p-4 border-2 rounded-lg cursor-pointer transition-all",
                settings.provider === "gemini"
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50",
                !geminiAvailable && "opacity-50 cursor-not-allowed"
              )}
              onClick={() => geminiAvailable && handleProviderChange("gemini")}
            >
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-md bg-blue-100 dark:bg-blue-900/20">
                  <Sparkles className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">Gemini CLI</h4>
                    {settings.provider === "gemini" && (
                      <Check className="w-4 h-4 text-primary" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Google's Gemini CLI for AI assistance
                  </p>
                  <div className="mt-2 text-xs">
                    {geminiAvailable ? (
                      <span className="text-green-600 dark:text-green-400">
                        ✓ {geminiInstallations.length} installation{geminiInstallations.length !== 1 ? 's' : ''} found
                      </span>
                    ) : (
                      <span className="text-muted-foreground">
                        No installations found
                      </span>
                    )}
                  </div>
                </div>
              </div>
              {saving && settings.provider === "gemini" && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-lg">
                  <Loader2 className="w-4 h-4 animate-spin" />
                </div>
              )}
            </motion.div>
          </div>

          {/* UI Preferences */}
          <div className="space-y-4 pt-4 border-t border-border">
            <h4 className="text-sm font-medium">UI Preferences</h4>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5 flex-1">
                <Label htmlFor="show-provider">Show provider indicator</Label>
                <p className="text-xs text-muted-foreground">
                  Display the current provider in the UI
                </p>
              </div>
              <Switch
                id="show-provider"
                checked={settings.ui.show_provider_indicator}
                onCheckedChange={(checked) => 
                  handleSettingChange("ui", { ...settings.ui, show_provider_indicator: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5 flex-1">
                <Label htmlFor="auto-switch">Auto-switch provider</Label>
                <p className="text-xs text-muted-foreground">
                  Automatically switch based on project configuration
                </p>
              </div>
              <Switch
                id="auto-switch"
                checked={settings.ui.auto_switch_provider}
                onCheckedChange={(checked) => 
                  handleSettingChange("ui", { ...settings.ui, auto_switch_provider: checked })
                }
              />
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
